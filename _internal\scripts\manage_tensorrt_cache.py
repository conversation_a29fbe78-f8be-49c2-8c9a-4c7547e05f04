#!/usr/bin/env python3
"""
TensorRT Engine Cache Management Utility

This script helps you manage TensorRT engine caching for the NGC OCR pipeline.

Usage:
    python manage_tensorrt_cache.py status     # Check cache status
    python manage_tensorrt_cache.py clear      # Clear cached engines (force rebuild)
    python manage_tensorrt_cache.py info       # Show detailed cache information
"""

import sys
from pathlib import Path
import shutil

def get_cache_path():
    """Get the TensorRT cache directory path"""
    return Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\tensorrt")

def check_cache_status():
    """Check if TensorRT engines are cached"""
    cache_path = get_cache_path()
    detection_engine = cache_path / "ocdnet_v2.4_fresh.trt"
    recognition_engine = cache_path / "ocrnet_v2.1.1_fresh.trt"
    
    print("🔍 TensorRT Engine Cache Status:")
    print(f"   Cache Directory: {cache_path}")
    print(f"   Directory Exists: {cache_path.exists()}")
    print()
    
    if detection_engine.exists():
        size_mb = detection_engine.stat().st_size // (1024*1024)
        print(f"✅ OCDNet Engine: {detection_engine.name} ({size_mb}MB)")
    else:
        print("❌ OCDNet Engine: Not found")
    
    if recognition_engine.exists():
        size_mb = recognition_engine.stat().st_size // (1024*1024)
        print(f"✅ OCRNet Engine: {recognition_engine.name} ({size_mb}MB)")
    else:
        print("❌ OCRNet Engine: Not found")
    
    print()
    
    if detection_engine.exists() and recognition_engine.exists():
        print("🚀 STATUS: TensorRT engines are cached - fast loading enabled!")
        print("   Next Stage 5 run will load engines in seconds vs ~6 minutes building")
    else:
        print("🔧 STATUS: Engines need to be built - first run will take ~6 minutes")
        print("   Subsequent runs will be much faster using cached engines")
    
    return detection_engine.exists() and recognition_engine.exists()

def clear_cache():
    """Clear cached TensorRT engines to force rebuild"""
    cache_path = get_cache_path()
    
    print("🗑️ Clearing TensorRT Engine Cache...")
    
    if not cache_path.exists():
        print("   Cache directory doesn't exist - nothing to clear")
        return
    
    engines_cleared = 0
    
    for engine_file in cache_path.glob("*.trt"):
        try:
            size_mb = engine_file.stat().st_size // (1024*1024)
            engine_file.unlink()
            print(f"   ❌ Deleted: {engine_file.name} ({size_mb}MB)")
            engines_cleared += 1
        except Exception as e:
            print(f"   ⚠️ Failed to delete {engine_file.name}: {e}")
    
    if engines_cleared > 0:
        print(f"✅ Cleared {engines_cleared} engine files")
        print("   Next Stage 5 run will rebuild engines (~6 minutes first time)")
    else:
        print("   No engine files found to clear")

def show_cache_info():
    """Show detailed cache information"""
    cache_path = get_cache_path()
    
    print("📊 TensorRT Cache Detailed Information:")
    print(f"   Cache Directory: {cache_path}")
    print()
    
    if not cache_path.exists():
        print("   Directory doesn't exist")
        return
    
    print("📁 Cache Contents:")
    total_size = 0
    file_count = 0
    
    for file_path in cache_path.iterdir():
        if file_path.is_file():
            size_bytes = file_path.stat().st_size
            size_mb = size_bytes // (1024*1024)
            total_size += size_bytes
            file_count += 1
            
            modified_time = file_path.stat().st_mtime
            import datetime
            modified_str = datetime.datetime.fromtimestamp(modified_time).strftime("%Y-%m-%d %H:%M:%S")
            
            print(f"   📄 {file_path.name}")
            print(f"      Size: {size_mb}MB ({size_bytes:,} bytes)")
            print(f"      Modified: {modified_str}")
            print()
    
    if file_count > 0:
        total_mb = total_size // (1024*1024)
        print(f"📊 Summary: {file_count} files, {total_mb}MB total")
    else:
        print("   No files in cache directory")
    
    print()
    print("💡 Cache Management Tips:")
    print("   - Engines are rebuilt only when missing or if force_rebuild=true in config")
    print("   - Clear cache if you update TensorRT, CUDA, or models")
    print("   - Cache saves ~6 minutes on every Stage 5 run after first build")

def main():
    if len(sys.argv) != 2:
        print(__doc__)
        return
    
    command = sys.argv[1].lower()
    
    if command == "status":
        check_cache_status()
    elif command == "clear":
        clear_cache()
        print()
        check_cache_status()
    elif command == "info":
        show_cache_info()
    else:
        print(f"Unknown command: {command}")
        print(__doc__)

if __name__ == "__main__":
    main()
