#!/usr/bin/env python3
"""
Practical TensorRT Optimization Script for RTX 5090
Creates optimized TensorRT engines using trtexec command-line tool
"""
import os
import subprocess
import time
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_rtx5090_tensorrt_optimization():
    """
    Run TensorRT optimization for RTX 5090 using trtexec
    This creates multiple engines optimized for different batch sizes
    """
    
    print("🚀 RTX 5090 TensorRT Optimization for NGC OCR Models")
    print("=" * 60)
    print()
    
    # Setup paths
    workspace = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator")
    models_dir = workspace / "_internal" / "models" / "ngc_ocr"
    trt_bin = workspace / "_internal" / "tools" / "TensorRT" / "bin" / "trtexec.exe"
    trt_lib = workspace / "_internal" / "tools" / "TensorRT" / "lib"
    
    # Verify paths
    if not trt_bin.exists():
        print(f"❌ trtexec not found: {trt_bin}")
        return False
        
    # ONNX model paths
    ocdnet_onnx = models_dir / "ocdnet_v2.4" / "ocdnet_vdeployable_onnx_v2.4" / "ocdnet_fan_tiny_2x_icdar_pruned.onnx"
    ocrnet_onnx = models_dir / "ocrnet_v2.1.1" / "ocrnet_vdeployable_v2.1.1" / "ocrnet-vit-pcb.onnx"
    
    if not ocdnet_onnx.exists():
        print(f"❌ OCDNet ONNX not found: {ocdnet_onnx}")
        return False
        
    if not ocrnet_onnx.exists():
        print(f"❌ OCRNet ONNX not found: {ocrnet_onnx}")
        return False
    
    # Create output directory
    tensorrt_dir = models_dir / "tensorrt"
    tensorrt_dir.mkdir(exist_ok=True)
    
    # Setup environment
    env = os.environ.copy()
    current_path = env.get('PATH', '')
    env['PATH'] = f"{current_path};{trt_lib}"
    
    print("📂 Model Verification:")
    print(f"   ✅ OCDNet ONNX: {ocdnet_onnx.name}")
    print(f"   ✅ OCRNet ONNX: {ocrnet_onnx.name}")
    print(f"   📁 Output: {tensorrt_dir}")
    print()
    
    # Define optimization configurations for different use cases
    ocdnet_configs = [
        {
            "name": "ocdnet_rtx5090_batch32_throughput.trt",
            "description": "High throughput - batch 32",
            "workspace": "8192M",
            "min_shapes": "input:1x3x720x1280",
            "opt_shapes": "input:16x3x720x1280", 
            "max_shapes": "input:32x3x1280x1920"
        },
        {
            "name": "ocdnet_rtx5090_batch8_balanced.trt", 
            "description": "Balanced - batch 8",
            "workspace": "4096M",
            "min_shapes": "input:1x3x720x1280",
            "opt_shapes": "input:4x3x720x1280",
            "max_shapes": "input:8x3x1280x1920"
        },
        {
            "name": "ocdnet_rtx5090_batch1_latency.trt",
            "description": "Low latency - batch 1", 
            "workspace": "2048M",
            "min_shapes": "input:1x3x720x1280",
            "opt_shapes": "input:1x3x720x1280",
            "max_shapes": "input:1x3x1280x1920"
        }
    ]\n    \n    ocrnet_configs = [\n        {\n            \"name\": \"ocrnet_rtx5090_batch64_throughput.trt\",\n            \"description\": \"High throughput - batch 64\",\n            \"workspace\": \"4096M\",\n            \"min_shapes\": \"input:1x1x64x200\",\n            \"opt_shapes\": \"input:32x1x64x200\",\n            \"max_shapes\": \"input:64x1x64x400\"\n        },\n        {\n            \"name\": \"ocrnet_rtx5090_batch16_balanced.trt\",\n            \"description\": \"Balanced - batch 16\", \n            \"workspace\": \"2048M\",\n            \"min_shapes\": \"input:1x1x64x200\",\n            \"opt_shapes\": \"input:8x1x64x200\",\n            \"max_shapes\": \"input:16x1x64x400\"\n        },\n        {\n            \"name\": \"ocrnet_rtx5090_batch4_latency.trt\",\n            \"description\": \"Low latency - batch 4\",\n            \"workspace\": \"1024M\", \n            \"min_shapes\": \"input:1x1x64x200\",\n            \"opt_shapes\": \"input:2x1x64x200\",\n            \"max_shapes\": \"input:4x1x64x400\"\n        }\n    ]\n    \n    # Convert OCDNet models\n    print(\"🔄 Converting OCDNet (Text Detection) Models...\")\n    print(\"-\" * 50)\n    \n    for config in ocdnet_configs:\n        engine_path = tensorrt_dir / config[\"name\"]\n        print(f\"\\n📝 Building: {config['description']}\")\n        \n        cmd = [\n            str(trt_bin),\n            f\"--onnx={ocdnet_onnx}\",\n            f\"--saveEngine={engine_path}\",\n            \n            # RTX 5090 Optimizations\n            \"--fp16\",                                        # FP16 precision for 2x speedup\n            \"--allowGPUFallback\",                           # GPU fallback\n            \"--builderOptimizationLevel=5\",                 # Maximum optimization\n            \"--hardwareCompatibilityLevel=ampere+\",         # RTX 5090 architecture\n            f\"--memPoolSize=workspace:{config['workspace']}\", # Workspace memory\n            \n            # Dynamic shapes for batching\n            f\"--minShapes={config['min_shapes']}\",\n            f\"--optShapes={config['opt_shapes']}\", \n            f\"--maxShapes={config['max_shapes']}\",\n            \n            # Performance optimizations\n            \"--useSpinWait\",                               # Reduce kernel overhead\n            \"--avgRuns=5\",                                 # Average timing\n            \"--warmUp=1000\",                               # GPU warmup\n            \n            # Skip inference testing to speed up build\n            \"--skipInference\"\n        ]\n        \n        print(f\"   Command: {' '.join([str(c) for c in cmd[:3]])} ... (+ optimization flags)\")\n        \n        try:\n            start_time = time.time()\n            result = subprocess.run(cmd, env=env, capture_output=True, text=True, cwd=workspace)\n            duration = time.time() - start_time\n            \n            if result.returncode == 0 and engine_path.exists():\n                size_mb = engine_path.stat().st_size / (1024 * 1024)\n                print(f\"   ✅ Success: {duration:.1f}s, {size_mb:.1f}MB\")\n            else:\n                print(f\"   ❌ Failed: {result.stderr[:200]}...\")\n                \n        except Exception as e:\n            print(f\"   ❌ Exception: {e}\")\n    \n    # Convert OCRNet models\n    print(\"\\n🔄 Converting OCRNet (Text Recognition) Models...\")\n    print(\"-\" * 50)\n    \n    for config in ocrnet_configs:\n        engine_path = tensorrt_dir / config[\"name\"]\n        print(f\"\\n📝 Building: {config['description']}\")\n        \n        cmd = [\n            str(trt_bin),\n            f\"--onnx={ocrnet_onnx}\",\n            f\"--saveEngine={engine_path}\",\n            \n            # RTX 5090 Optimizations\n            \"--fp16\",\n            \"--allowGPUFallback\", \n            \"--builderOptimizationLevel=5\",\n            \"--hardwareCompatibilityLevel=ampere+\",\n            f\"--memPoolSize=workspace:{config['workspace']}\",\n            \n            # Dynamic shapes for text recognition\n            f\"--minShapes={config['min_shapes']}\",\n            f\"--optShapes={config['opt_shapes']}\",\n            f\"--maxShapes={config['max_shapes']}\",\n            \n            # Performance optimizations\n            \"--useSpinWait\",\n            \"--avgRuns=5\",\n            \"--warmUp=1000\",\n            \"--skipInference\"\n        ]\n        \n        print(f\"   Command: {' '.join([str(c) for c in cmd[:3]])} ... (+ optimization flags)\")\n        \n        try:\n            start_time = time.time()\n            result = subprocess.run(cmd, env=env, capture_output=True, text=True, cwd=workspace)\n            duration = time.time() - start_time\n            \n            if result.returncode == 0 and engine_path.exists():\n                size_mb = engine_path.stat().st_size / (1024 * 1024)\n                print(f\"   ✅ Success: {duration:.1f}s, {size_mb:.1f}MB\")\n            else:\n                print(f\"   ❌ Failed: {result.stderr[:200]}...\")\n                \n        except Exception as e:\n            print(f\"   ❌ Exception: {e}\")\n    \n    # Summary\n    print(\"\\n📊 TensorRT Optimization Summary\")\n    print(\"=\" * 40)\n    \n    created_engines = list(tensorrt_dir.glob(\"*rtx5090*.trt\"))\n    \n    if created_engines:\n        print(f\"✅ Created {len(created_engines)} optimized engines:\")\n        for engine in created_engines:\n            size_mb = engine.stat().st_size / (1024 * 1024)\n            print(f\"   📦 {engine.name} ({size_mb:.1f}MB)\")\n        \n        print(\"\\n🎯 Performance Expectations:\")\n        print(\"   • Text Detection: 500-1500 FPS (batch processing)\")\n        print(\"   • Text Recognition: 1000-3000 FPS (batch processing)\")\n        print(\"   • Overall Speedup: 5-50x vs ONNX Runtime\")\n        print(\"   • Memory Usage: <20GB VRAM (conservative)\")\n        \n        print(\"\\n🔧 Integration:\")\n        print(\"   1. Update ngc_ocr_pipeline.py to use optimized engines\")\n        print(\"   2. Set use_tensorrt: true in configuration\")\n        print(\"   3. Choose appropriate batch sizes for your workload\")\n        \n        return True\n    else:\n        print(\"❌ No engines were created successfully\")\n        print(\"\\n💡 Troubleshooting:\")\n        print(\"   • Check CUDA installation (CUDA 12.x required)\")\n        print(\"   • Verify TensorRT installation\")\n        print(\"   • Ensure sufficient disk space\")\n        print(\"   • Try running as administrator\")\n        \n        return False\n\ndef update_pipeline_configuration():\n    \"\"\"\n    Update the NGC OCR pipeline configuration to use TensorRT engines\n    \"\"\"\n    print(\"\\n🔧 Updating Pipeline Configuration...\")\n    \n    config_updates = '''\n# RTX 5090 TensorRT Configuration\n# Add this to your ngc_ocr_pipeline.py configuration:\n\nRTX5090_TENSORRT_CONFIG = {\n    \"use_tensorrt\": True,\n    \"tensorrt_engines\": {\n        # Detection engines (choose based on workload)\n        \"detection_high_throughput\": \"ocdnet_rtx5090_batch32_throughput.trt\",\n        \"detection_balanced\": \"ocdnet_rtx5090_batch8_balanced.trt\", \n        \"detection_low_latency\": \"ocdnet_rtx5090_batch1_latency.trt\",\n        \n        # Recognition engines\n        \"recognition_high_throughput\": \"ocrnet_rtx5090_batch64_throughput.trt\",\n        \"recognition_balanced\": \"ocrnet_rtx5090_batch16_balanced.trt\",\n        \"recognition_low_latency\": \"ocrnet_rtx5090_batch4_latency.trt\"\n    },\n    \n    # Automatic engine selection based on workload size\n    \"auto_engine_selection\": {\n        \"large_workload_threshold\": 100,    # Use high throughput engines\n        \"medium_workload_threshold\": 20,    # Use balanced engines  \n        \"small_workload_threshold\": 5       # Use low latency engines\n    },\n    \n    # RTX 5090 specific optimizations\n    \"rtx5090_optimizations\": {\n        \"fp16_precision\": True,\n        \"cuda_graphs\": True,\n        \"memory_pool_size_gb\": 4,\n        \"max_workspace_size_gb\": 8,\n        \"pin_memory\": True,\n        \"non_blocking_transfers\": True\n    }\n}\n\n# Usage in your pipeline:\n# 1. Check if TensorRT engines exist\n# 2. Select appropriate engines based on workload size  \n# 3. Fall back to ONNX if TensorRT not available\n'''\n    \n    config_file = Path(r\"C:\\Users\\<USER>\\Videos\\PlexMovieAutomator\\_internal\\config\\rtx5090_tensorrt_config.py\")\n    config_file.write_text(config_updates)\n    \n    print(f\"📁 Configuration written to: {config_file}\")\n    \ndef benchmark_engines():\n    \"\"\"\n    Benchmark the created TensorRT engines\n    \"\"\"\n    print(\"\\n🏃 Benchmarking TensorRT Engines...\")\n    \n    workspace = Path(r\"C:\\Users\\<USER>\\Videos\\PlexMovieAutomator\")\n    trt_bin = workspace / \"_internal\" / \"tools\" / \"TensorRT\" / \"bin\" / \"trtexec.exe\"\n    tensorrt_dir = workspace / \"_internal\" / \"models\" / \"ngc_ocr\" / \"tensorrt\"\n    \n    engines = list(tensorrt_dir.glob(\"*rtx5090*.trt\"))\n    \n    if not engines:\n        print(\"❌ No RTX 5090 optimized engines found\")\n        return\n        \n    for engine in engines:\n        print(f\"\\n🔍 Benchmarking {engine.name}...\")\n        \n        cmd = [\n            str(trt_bin),\n            f\"--loadEngine={engine}\",\n            \"--iterations=50\",\n            \"--avgRuns=5\",\n            \"--warmUp=500\",\n            \"--duration=5\"\n        ]\n        \n        try:\n            result = subprocess.run(cmd, capture_output=True, text=True)\n            if result.returncode == 0:\n                # Parse performance info from output\n                lines = result.stdout.split('\\n')\n                for line in lines:\n                    if 'mean:' in line and 'ms' in line:\n                        print(f\"   ⚡ {line.strip()}\")\n                        break\n                else:\n                    print(\"   ✅ Benchmark completed (check logs for details)\")\n            else:\n                print(f\"   ❌ Benchmark failed\")\n        except Exception as e:\n            print(f\"   ❌ Exception: {e}\")\n\ndef main():\n    \"\"\"\n    Main function to run RTX 5090 TensorRT optimization\n    \"\"\"\n    try:\n        # Step 1: Create optimized engines\n        success = run_rtx5090_tensorrt_optimization()\n        \n        if success:\n            # Step 2: Update configuration \n            update_pipeline_configuration()\n            \n            # Step 3: Benchmark engines\n            benchmark_engines()\n            \n            print(\"\\n🎉 RTX 5090 TensorRT optimization complete!\")\n            print(\"\\n🚀 Next Steps:\")\n            print(\"   1. Test the optimized pipeline with real subtitle images\")\n            print(\"   2. Monitor GPU utilization and adjust batch sizes if needed\")\n            print(\"   3. Compare performance with original ONNX pipeline\")\n            \n        else:\n            print(\"\\n❌ Optimization failed\")\n            print(\"\\n🔄 Fallback options:\")\n            print(\"   • Use existing ONNX models (still fast with CUDA)\")\n            print(\"   • Try simpler TensorRT conversion with basic flags\")\n            print(\"   • Check system requirements and dependencies\")\n        \n        return success\n        \n    except Exception as e:\n        print(f\"\\n❌ Fatal error during optimization: {e}\")\n        return False\n\nif __name__ == \"__main__\":\n    main()\n    input(\"\\nPress Enter to continue...\")
