#!/usr/bin/env python3
"""
TensorRT Engine Inspector for NGC OCRNet v2.1.1
This script inspects the TensorRT engine to understand the actual input/output shapes
and identify why we're getting single character outputs instead of sequences.
"""

import os
import sys
import logging
import numpy as np
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)8s] %(message)s')
logger = logging.getLogger(__name__)

def inspect_tensorrt_engine():
    """Inspect the TensorRT OCRNet engine to understand input/output specifications"""
    
    # TensorRT engine paths
    models_dir = Path("models/ngc_ocr")
    detection_engine = models_dir / "ocdnet_detection_trt.engine"
    recognition_engine = models_dir / "ocrnet_recognition_trt.engine"
    
    logger.info("🔍 TensorRT Engine Inspector for NGC OCRNet v2.1.1")
    logger.info("=" * 60)
    
    try:
        import tensorrt as trt
        
        # Initialize TensorRT
        TRT_LOGGER = trt.Logger(trt.Logger.INFO)
        
        # Inspect Recognition Engine (the problematic one)
        logger.info("📋 RECOGNITION ENGINE ANALYSIS")
        logger.info(f"Engine file: {recognition_engine}")
        
        if not recognition_engine.exists():
            logger.error(f"❌ Recognition engine not found: {recognition_engine}")
            return
            
        # Load and inspect the recognition engine
        with open(recognition_engine, 'rb') as f:
            engine_data = f.read()
            
        runtime = trt.Runtime(TRT_LOGGER)
        engine = runtime.deserialize_cuda_engine(engine_data)
        
        if engine is None:
            logger.error("❌ Failed to load TensorRT engine")
            return
            
        logger.info(f"✅ Engine loaded successfully")
        logger.info(f"   Number of bindings: {engine.num_bindings}")
        logger.info(f"   Max batch size: {engine.max_batch_size}")
        
        # Analyze each binding (inputs and outputs)
        for i in range(engine.num_bindings):
            binding_name = engine.get_binding_name(i)
            binding_shape = engine.get_binding_shape(i)
            binding_dtype = engine.get_binding_dtype(i)
            is_input = engine.binding_is_input(i)
            
            binding_type = "INPUT" if is_input else "OUTPUT"
            logger.info(f"")
            logger.info(f"🔗 Binding {i}: {binding_name}")
            logger.info(f"   Type: {binding_type}")
            logger.info(f"   Shape: {binding_shape}")
            logger.info(f"   Data type: {binding_dtype}")
            
            # Check for dynamic shapes
            if -1 in binding_shape:
                logger.info(f"   🔄 Dynamic shape detected!")
                # Try to get optimization profile shapes
                profile = engine.get_profile_shape(0, i) if engine.num_optimization_profiles > 0 else None
                if profile:
                    min_shape, opt_shape, max_shape = profile
                    logger.info(f"   Min shape: {min_shape}")
                    logger.info(f"   Opt shape: {opt_shape}")
                    logger.info(f"   Max shape: {max_shape}")
        
        logger.info("")
        logger.info("📊 ENGINE SUMMARY")
        logger.info(f"   Optimization profiles: {engine.num_optimization_profiles}")
        
        # Check if this is a sequence model
        input_shape = None
        output_shapes = []
        
        for i in range(engine.num_bindings):
            shape = engine.get_binding_shape(i)
            if engine.binding_is_input(i):
                input_shape = shape
            else:
                output_shapes.append(shape)
        
        logger.info(f"   Input shape: {input_shape}")
        logger.info(f"   Output shapes: {output_shapes}")
        
        # Analyze the shapes for sequence modeling
        if input_shape and len(input_shape) == 4:  # [batch, channels, height, width]
            batch, channels, height, width = input_shape
            logger.info(f"")
            logger.info(f"🎯 INPUT ANALYSIS:")
            logger.info(f"   Format: [batch={batch}, channels={channels}, height={height}, width={width}]")
            
            if width == 200:
                logger.info(f"   ✅ Fixed width confirmed: 200 pixels")
            elif width == -1:
                logger.info(f"   🔄 Dynamic width detected")
            
            if height == 64:
                logger.info(f"   ✅ Fixed height confirmed: 64 pixels")
                
        # Analyze output for sequence information
        logger.info(f"")
        logger.info(f"🎯 OUTPUT ANALYSIS:")
        for i, shape in enumerate(output_shapes):
            logger.info(f"   Output {i}: {shape}")
            
            if len(shape) == 2:  # Likely [sequence_length, vocab_size]
                seq_len, vocab_size = shape
                logger.info(f"     Format: [sequence_length={seq_len}, vocab_size={vocab_size}]")
                
                if seq_len == 1:
                    logger.warning(f"     ⚠️  PROBLEM: Sequence length is fixed at 1!")
                    logger.warning(f"     This explains single character outputs!")
                elif seq_len == -1:
                    logger.info(f"     ✅ Dynamic sequence length (good for variable text)")
                else:
                    logger.info(f"     📏 Fixed sequence length: {seq_len}")
                    
            elif len(shape) == 3:  # Possibly [batch, sequence_length, vocab_size]
                batch, seq_len, vocab_size = shape
                logger.info(f"     Format: [batch={batch}, sequence_length={seq_len}, vocab_size={vocab_size}]")
                
                if seq_len == 1:
                    logger.warning(f"     ⚠️  PROBLEM: Sequence length is fixed at 1!")
                    logger.warning(f"     This explains single character outputs!")
                    
        logger.info("")
        logger.info("🔧 DIAGNOSIS COMPLETE")
        
        # Provide specific recommendations
        if any(1 in shape for shape in output_shapes):
            logger.error("❌ CRITICAL ISSUE FOUND:")
            logger.error("   The TensorRT engine has sequence_length=1 in output shape")
            logger.error("   This means it can only predict 1 character per text region")
            logger.error("   We need to rebuild the engine with proper sequence modeling")
            logger.error("")
            logger.error("🔧 SOLUTIONS:")
            logger.error("   1. Rebuild TensorRT engine with dynamic sequence length")
            logger.error("   2. Or use sliding window approach with character-level outputs")
            logger.error("   3. Or switch to a different OCR model architecture")
        else:
            logger.info("✅ Engine shapes look correct for sequence modeling")
            
    except ImportError:
        logger.error("❌ TensorRT not available. Install with: pip install tensorrt")
    except Exception as e:
        logger.error(f"❌ Engine inspection failed: {e}")
        import traceback
        logger.debug(traceback.format_exc())

def test_current_model_behavior():
    """Test the current model to see what's actually happening"""
    
    logger.info("")
    logger.info("🧪 TESTING CURRENT MODEL BEHAVIOR")
    logger.info("=" * 60)
    
    try:
        # Import ONNX Runtime directly to inspect the model
        import onnxruntime as ort
        
        # Path to the recognition model
        recognition_model_path = "models/ngc_ocr/ocrnet_v2.1.1/ocrnet_vdeployable_v2.1.1/ocrnet-vit-pcb.onnx"
        
        if not os.path.exists(recognition_model_path):
            logger.error(f"❌ Recognition model not found: {recognition_model_path}")
            return
        
        # Initialize ONNX Runtime session
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        recognition_session = ort.InferenceSession(recognition_model_path, providers=providers)
        
        if not recognition_session:
            logger.error("❌ Failed to initialize recognition model")
            return
            
        # Get input/output info
        input_details = recognition_session.get_inputs()[0]
        output_details = recognition_session.get_outputs()
        
        logger.info(f"📋 ONNX RUNTIME MODEL INFO:")
        logger.info(f"   Input: {input_details.name}, shape: {input_details.shape}, type: {input_details.type}")
        
        for i, output in enumerate(output_details):
            logger.info(f"   Output {i}: {output.name}, shape: {output.shape}, type: {output.type}")
        
        # Create test input: simple text image (white text on black background)
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a test image with "HELLO" text
        test_img = Image.new('RGB', (200, 64), color='black')
        draw = ImageDraw.Draw(test_img)
        
        try:
            # Try to use default font
            font = ImageFont.load_default()
            draw.text((10, 20), "HELLO", fill='white', font=font)
        except:
            # Fallback: simple text without font
            draw.text((10, 20), "HELLO", fill='white')
        
        # Convert to model input format
        test_array = np.array(test_img.convert('L'), dtype=np.float32) / 255.0
        test_batch = np.expand_dims(np.expand_dims(test_array, axis=0), axis=0)
        
        logger.info(f"📋 TEST INPUT:")
        logger.info(f"   Input shape: {test_batch.shape}")
        logger.info(f"   Input range: [{test_batch.min():.3f}, {test_batch.max():.3f}]")
        
        # Run inference
        input_name = input_details.name
        outputs = recognition_session.run(None, {input_name: test_batch})
        
        logger.info(f"📋 TEST OUTPUT:")
        for i, output in enumerate(outputs):
            logger.info(f"   Output {i} shape: {output.shape}")
            logger.info(f"   Output {i} range: [{output.min():.3f}, {output.max():.3f}]")
            
            if len(output.shape) == 2 and output.shape[0] == 1:
                # This is likely [batch=1, sequence_length=X] or [sequence_length=1, vocab_size=X]
                if output.shape[1] <= 100:  # Probably sequence_length
                    logger.warning(f"     ⚠️  Shape suggests sequence_length={output.shape[1]}")
                else:  # Probably vocab_size
                    logger.warning(f"     ⚠️  Shape suggests vocab_size={output.shape[1]}")
        
        # Try to decode the output using simple CTC decoding
        vocabulary = [
            '<blank>',  # 0 - CTC blank token
            ' ',        # 1 - space
            '!', '"', '#', '$', '%', '&', "'", '(', ')', '*', '+', ',', '-', '.', '/',
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            ':', ';', '<', '=', '>', '?', '@',
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 
            'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            '[', '\\', ']', '^', '_', '`',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
            'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
            '{', '|', '}', '~'
        ]
        
        def simple_ctc_decode(outputs, vocab):
            """Simple CTC decoding to understand model output"""
            logits = outputs[0]  # Primary output
            if len(logits.shape) == 3:
                logits = logits[0]  # Remove batch dimension
            
            if len(logits.shape) == 2:
                # Shape is [sequence_length, vocab_size]
                char_indices = np.argmax(logits, axis=-1)
                recognized_chars = []
                prev_idx = -1
                
                for idx in char_indices:
                    if 0 <= idx < len(vocab):
                        char = vocab[idx]
                        if char != '<blank>' and idx != prev_idx:
                            recognized_chars.append(char)
                        prev_idx = idx
                
                return ''.join(recognized_chars)
            elif len(logits.shape) == 1:
                # Single character prediction
                char_idx = np.argmax(logits)
                if 0 <= char_idx < len(vocab):
                    return vocab[char_idx] if vocab[char_idx] != '<blank>' else ""
                return ""
            else:
                return ""
        
        decoded_text = simple_ctc_decode(outputs, vocabulary)
        
        logger.info(f"📋 DECODED TEXT: '{decoded_text}'")
        
        if len(decoded_text) <= 1:
            logger.error("❌ CONFIRMED: Model only outputs single characters")
            logger.error("   This confirms the sequence_length=1 issue")
        else:
            logger.info("✅ Model can output multiple characters")
            
    except Exception as e:
        logger.error(f"❌ Model behavior test failed: {e}")
        import traceback
        logger.debug(traceback.format_exc())

if __name__ == "__main__":
    logger.info("🚀 Starting TensorRT Engine Diagnosis")
    logger.info("This will help identify why OCR outputs single characters instead of full text")
    logger.info("")
    
    # Inspect the TensorRT engine
    inspect_tensorrt_engine()
    
    # Test current model behavior
    test_current_model_behavior()
    
    logger.info("")
    logger.info("🎯 Diagnosis complete! Check the output above for issues and solutions.")
