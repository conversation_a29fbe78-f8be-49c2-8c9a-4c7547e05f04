import onnx
import os

def inspect_onnx_model(model_path):
    """Inspect ONNX model input/output dimensions"""
    try:
        print(f"\n🔍 Inspecting ONNX Model: {os.path.basename(model_path)}")
        print(f"📁 Path: {model_path}")
        print("=" * 80)
        
        # Load the ONNX model
        model = onnx.load(model_path)
        
        # Check the model
        onnx.checker.check_model(model)
        
        # Get model info
        print("\n📊 Model Information:")
        print(f"  IR Version: {model.ir_version}")
        print(f"  Producer Name: {model.producer_name}")
        print(f"  Producer Version: {model.producer_version}")
        print(f"  Opset Version: {model.opset_import[0].version}")
        
        # Get input information
        print("\n📥 Input Tensors:")
        for i, input_tensor in enumerate(model.graph.input):
            print(f"  Input {i}: {input_tensor.name}")
            shape = []
            for dim in input_tensor.type.tensor_type.shape.dim:
                if dim.dim_value:
                    shape.append(dim.dim_value)
                elif dim.dim_param:
                    shape.append(f"dynamic({dim.dim_param})")
                else:
                    shape.append("unknown")
            print(f"    Shape: {shape}")
            print(f"    Type: {input_tensor.type.tensor_type.elem_type}")
        
        # Get output information
        print("\n📤 Output Tensors:")
        for i, output_tensor in enumerate(model.graph.output):
            print(f"  Output {i}: {output_tensor.name}")
            shape = []
            for dim in output_tensor.type.tensor_type.shape.dim:
                if dim.dim_value:
                    shape.append(dim.dim_value)
                elif dim.dim_param:
                    shape.append(f"dynamic({dim.dim_param})")
                else:
                    shape.append("unknown")
            print(f"    Shape: {shape}")
            print(f"    Type: {output_tensor.type.tensor_type.elem_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error inspecting model: {e}")
        return False

def main():
    """Inspect both NGC OCR models"""
    
    models = [
        {
            "name": "OCDNet (Detection)",
            "path": r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocdnet_v2.4\ocdnet_vdeployable_onnx_v2.4\ocdnet_fan_tiny_2x_icdar_pruned.onnx"
        },
        {
            "name": "OCRNet (Recognition)", 
            "path": r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocrnet_v2.1.1\ocrnet_vdeployable_v2.1.1\ocrnet-vit-pcb.onnx"
        }
    ]
    
    for model in models:
        print(f"\n{'='*80}")
        print(f"🔍 INSPECTING: {model['name']}")
        print(f"{'='*80}")
        
        if os.path.exists(model['path']):
            inspect_onnx_model(model['path'])
        else:
            print(f"❌ Model not found: {model['path']}")
    
    print(f"\n{'='*80}")
    print("✅ ONNX Model Inspection Complete!")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
