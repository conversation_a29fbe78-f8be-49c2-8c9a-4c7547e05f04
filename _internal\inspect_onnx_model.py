#!/usr/bin/env python3
"""
Inspect ONNX model to understand its input/output shapes
"""

import onnx
import sys
sys.path.append('.')

def inspect_onnx_model(model_path):
    """Inspect ONNX model structure"""
    print(f"🔍 Inspecting ONNX model: {model_path}")
    
    try:
        model = onnx.load(model_path)
        
        print(f"   📋 Model version: {model.model_version}")
        print(f"   🏭 Producer: {model.producer_name} {model.producer_version}")
        print(f"   📦 IR version: {model.ir_version}")
        
        # Check inputs
        print(f"\n📥 Model Inputs:")
        for i, input_tensor in enumerate(model.graph.input):
            print(f"   Input {i}: {input_tensor.name}")
            print(f"      Type: {input_tensor.type}")
            if hasattr(input_tensor.type, 'tensor_type'):
                shape = []
                for dim in input_tensor.type.tensor_type.shape.dim:
                    if hasattr(dim, 'dim_value') and dim.dim_value > 0:
                        shape.append(dim.dim_value)
                    elif hasattr(dim, 'dim_param'):
                        shape.append(f"'{dim.dim_param}'")
                    else:
                        shape.append("?")
                print(f"      Shape: {shape}")
        
        # Check outputs
        print(f"\n📤 Model Outputs:")
        for i, output_tensor in enumerate(model.graph.output):
            print(f"   Output {i}: {output_tensor.name}")
            print(f"      Type: {output_tensor.type}")
            if hasattr(output_tensor.type, 'tensor_type'):
                shape = []
                for dim in output_tensor.type.tensor_type.shape.dim:
                    if hasattr(dim, 'dim_value') and dim.dim_value > 0:
                        shape.append(dim.dim_value)
                    elif hasattr(dim, 'dim_param'):
                        shape.append(f"'{dim.dim_param}'")
                    else:
                        shape.append("?")
                print(f"      Shape: {shape}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error inspecting model: {e}")
        return False

if __name__ == "__main__":
    # Inspect both models
    ocdnet_path = r'C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocdnet_v2.4\ocdnet_vdeployable_onnx_v2.4\ocdnet_fan_tiny_2x_icdar_pruned.onnx'
    ocrnet_path = r'C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocrnet_v2.1.1\ocrnet_vdeployable_v2.1.1\ocrnet-vit-pcb.onnx'
    
    print("=" * 60)
    inspect_onnx_model(ocdnet_path)
    
    print("\n" + "=" * 60)
    inspect_onnx_model(ocrnet_path)
