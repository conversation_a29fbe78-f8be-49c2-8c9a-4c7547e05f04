#!/usr/bin/env python3
"""
Test fresh TensorRT engines to verify they produce valid outputs (no NaN values)
"""

import sys
sys.path.append('.')

import numpy as np
from utils.ngc_ocr_pipeline import _build_tensorrt_engines_from_onnx
from pathlib import Path

print("🧪 Testing fresh TensorRT engines for NaN-free inference...")

# Model paths
ocdnet_path = r'C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocdnet_v2.4\ocdnet_vdeployable_onnx_v2.4\ocdnet_fan_tiny_2x_icdar_pruned.onnx'
ocrnet_path = r'C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocrnet_v2.1.1\ocrnet_vdeployable_v2.1.1\ocrnet-vit-pcb.onnx'
cache_dir = r'C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\cache\tensorrt'

print("🔄 Loading fresh TensorRT engines...")

# Load the engines we just built
detection_session, recognition_session = _build_tensorrt_engines_from_onnx(
    ocdnet_path, 
    ocrnet_path, 
    cache_dir
)

if not detection_session or not recognition_session:
    print("❌ Failed to load engines")
    exit(1)

print("✅ Engines loaded successfully!")
print("")

# Test OCDNet detection engine
print("🧪 Testing OCDNet detection engine...")
print("📐 Creating test input tensor [1, 3, 736, 1280]...")

# Create dummy input tensor matching OCDNet input requirements
test_input = np.random.rand(1, 3, 736, 1280).astype(np.float32)
test_input = np.ascontiguousarray(test_input)

print(f"📊 Input tensor stats: min={test_input.min():.3f}, max={test_input.max():.3f}, mean={test_input.mean():.3f}")

try:
    # Get input name
    input_spec = detection_session.get_inputs()[0]
    input_name = input_spec.name
    print(f"🎯 Input name: {input_name}")
    
    # Run inference
    print("🚀 Running OCDNet inference...")
    detection_outputs = detection_session.run(None, {input_name: test_input})
    
    print(f"✅ OCDNet inference successful!")
    print(f"📊 Output count: {len(detection_outputs)}")
    
    # Check for NaN values in outputs
    for i, output in enumerate(detection_outputs):
        nan_count = np.isnan(output).sum()
        inf_count = np.isinf(output).sum()
        finite_count = np.isfinite(output).sum()
        total_count = output.size
        
        print(f"   Output {i}: shape {output.shape}, dtype {output.dtype}")
        print(f"   Values: {finite_count}/{total_count} finite, {nan_count} NaN, {inf_count} Inf")
        print(f"   Range: [{output.min():.6f}, {output.max():.6f}]")
        
        if nan_count == 0:
            print(f"   ✅ No NaN values found in output {i}!")
        else:
            print(f"   ❌ WARNING: {nan_count} NaN values in output {i}")

except Exception as e:
    print(f"❌ OCDNet inference failed: {e}")

print("")

# Test OCRNet recognition engine
print("🧪 Testing OCRNet recognition engine...")
print("📐 Creating test input tensor [1, 1, 64, 200]...")

# Create dummy input tensor matching OCRNet input requirements
test_input_ocr = np.random.rand(1, 1, 64, 200).astype(np.float32)
test_input_ocr = np.ascontiguousarray(test_input_ocr)

print(f"📊 Input tensor stats: min={test_input_ocr.min():.3f}, max={test_input_ocr.max():.3f}, mean={test_input_ocr.mean():.3f}")

try:
    # Get input name
    input_spec_ocr = recognition_session.get_inputs()[0]
    input_name_ocr = input_spec_ocr.name
    print(f"🎯 Input name: {input_name_ocr}")
    
    # Run inference
    print("🚀 Running OCRNet inference...")
    recognition_outputs = recognition_session.run(None, {input_name_ocr: test_input_ocr})
    
    print(f"✅ OCRNet inference successful!")
    print(f"📊 Output count: {len(recognition_outputs)}")
    
    # Check for NaN values in outputs
    for i, output in enumerate(recognition_outputs):
        nan_count = np.isnan(output).sum()
        inf_count = np.isinf(output).sum()
        finite_count = np.isfinite(output).sum()
        total_count = output.size
        
        print(f"   Output {i}: shape {output.shape}, dtype {output.dtype}")
        print(f"   Values: {finite_count}/{total_count} finite, {nan_count} NaN, {inf_count} Inf")
        print(f"   Range: [{output.min():.6f}, {output.max():.6f}]")
        
        if nan_count == 0:
            print(f"   ✅ No NaN values found in output {i}!")
        else:
            print(f"   ❌ WARNING: {nan_count} NaN values in output {i}")

except Exception as e:
    print(f"❌ OCRNet inference failed: {e}")

print("")

# Cleanup
print("🧹 Cleaning up resources...")
if detection_session:
    detection_session.close()
    print("✅ Detection session cleanup completed")

if recognition_session:
    recognition_session.close()
    print("✅ Recognition session cleanup completed")

print("")
print("🎉 Fresh TensorRT engine validation completed!")
print("📈 Engines should now produce valid probability maps for text detection")
