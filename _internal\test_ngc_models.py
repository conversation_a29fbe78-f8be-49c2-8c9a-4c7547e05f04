#!/usr/bin/env python3
"""
Quick NGC Models Test - Verify NVIDIA pre-trained models work
Tests both OCDNet (text detection) and OCRNet (text recognition) with correct NVIDIA specs
"""

import sys
import logging
import numpy as np
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_ngc_models():
    """Test NGC models with correct NVIDIA specifications"""
    logger.info("🧪 Testing NGC Models with NVIDIA-specified input shapes...")
    
    try:
        # Import required libraries
        import onnxruntime as ort
        from PIL import Image
        logger.info("✅ Required libraries imported successfully")
        
        # Model paths from your configuration
        ocdnet_path = r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocdnet_v2.4\ocdnet_vdeployable_onnx_v2.4\ocdnet_fan_tiny_2x_icdar_pruned.onnx"
        ocrnet_path = r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocrnet_v2.1.1\ocrnet_vdeployable_v2.1.1\ocrnet-vit-pcb.onnx"
        
        # Check if model files exist
        ocdnet_exists = Path(ocdnet_path).exists()
        ocrnet_exists = Path(ocrnet_path).exists()
        
        logger.info(f"📁 OCDNet model: {'✅ Found' if ocdnet_exists else '❌ Missing'}")
        logger.info(f"📁 OCRNet model: {'✅ Found' if ocrnet_exists else '❌ Missing'}")
        
        if not ocdnet_exists:
            logger.error(f"OCDNet model not found at: {ocdnet_path}")
        if not ocrnet_exists:
            logger.error(f"OCRNet model not found at: {ocrnet_path}")
            
        if not (ocdnet_exists and ocrnet_exists):
            logger.error("❌ Cannot test models - files missing")
            return False
        
        # Setup ONNX Runtime providers
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        if 'CUDAExecutionProvider' in ort.get_available_providers():
            logger.info("🎮 CUDA provider available - will use GPU acceleration")
        else:
            logger.info("🖥️ Using CPU provider")
            providers = ['CPUExecutionProvider']
        
        # Test OCDNet (Text Detection)
        logger.info("\n🔍 Testing OCDNet (Text Detection)...")
        try:
            # Load OCDNet with correct input shape
            detection_session = ort.InferenceSession(ocdnet_path, providers=providers)
            
            # Get actual input shape from model
            input_details = detection_session.get_inputs()[0]
            logger.info(f"   Model input name: {input_details.name}")
            logger.info(f"   Model input shape: {input_details.shape}")
            
            # Create test input matching YOUR ACTUAL MODEL (not generic NVIDIA specs)
            # Your OCDNet model expects: [batch, 3, 736, 1280]
            test_input_shape = (1, 3, 736, 1280)  # Use YOUR model's actual dimensions
            test_input = np.random.rand(*test_input_shape).astype(np.float32)
            
            logger.info(f"   Test input shape: {test_input.shape}")
            
            # Run inference
            outputs = detection_session.run(None, {input_details.name: test_input})
            logger.info(f"   ✅ OCDNet inference successful!")
            logger.info(f"   Output shapes: {[output.shape for output in outputs]}")
            
        except Exception as e:
            logger.error(f"   ❌ OCDNet test failed: {e}")
            return False
        
        # Test OCRNet (Text Recognition)
        logger.info("\n📝 Testing OCRNet (Text Recognition)...")
        try:
            # Load OCRNet with correct input shape
            recognition_session = ort.InferenceSession(ocrnet_path, providers=providers)
            
            # Get actual input shape from model
            input_details = recognition_session.get_inputs()[0]
            logger.info(f"   Model input name: {input_details.name}")
            logger.info(f"   Model input shape: {input_details.shape}")
            
            # Create test input matching NVIDIA specs
            # NVIDIA spec for v2.1: "Gray Images of 1 X 64 X 200 (C H W)"
            test_input_shape = (1, 1, 64, 200)  # Batch=1, Channels=1 (grayscale), Height=64, Width=200
            test_input = np.random.rand(*test_input_shape).astype(np.float32)
            
            logger.info(f"   Test input shape: {test_input.shape}")
            
            # Run inference
            outputs = recognition_session.run(None, {input_details.name: test_input})
            logger.info(f"   ✅ OCRNet inference successful!")
            logger.info(f"   Output shapes: {[output.shape for output in outputs]}")
            
        except Exception as e:
            logger.error(f"   ❌ OCRNet test failed: {e}")
            return False
        
        logger.info("\n🎉 NGC MODELS TEST SUCCESSFUL!")
        logger.info("   Both OCDNet and OCRNet models loaded and ran inference correctly")
        logger.info("   ✅ Your pre-trained NVIDIA models are working!")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.error("Install with: pip install onnxruntime-gpu torch pillow")
        return False
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

def test_input_shapes():
    """Test different input shapes based on NVIDIA documentation"""
    logger.info("\n📏 Testing NVIDIA-specified input shapes...")
    
    # NVIDIA specs from your documentation:
    shapes = {
        "OCDNet (Text Detection)": {
            "Standard": (1, 3, 640, 640),
            "Alternative 1": (1, 3, 736, 1280),  # From your existing config
            "Alternative 2": (1, 3, 512, 512),   # Smaller, still multiple of 32
        },
        "OCRNet (Text Recognition)": {
            "v2.1 (PCB/VIT)": (1, 1, 64, 200),   # Your current model version
            "v1.0 (ResNet)": (1, 1, 32, 100),    # Alternative version
        }
    }
    
    for model_name, shape_variants in shapes.items():
        logger.info(f"\n{model_name}:")
        for variant_name, shape in shape_variants.items():
            logger.info(f"   {variant_name}: {shape}")
    
    logger.info("\n💡 Key points from NVIDIA documentation:")
    logger.info("   • OCDNet: Height and Width must be multiples of 32")
    logger.info("   • OCRNet v2.1: Uses 1x64x200 for grayscale images")
    logger.info("   • Batch size should be 1 for TensorRT engines")
    logger.info("   • FP16 precision recommended for RTX 5090")

if __name__ == "__main__":
    logger.info("🚀 NGC Models Verification Test")
    logger.info("=" * 50)
    
    # Test input shapes first
    test_input_shapes()
    
    # Test actual models
    success = test_ngc_models()
    
    if success:
        logger.info("\n🎯 RESULT: Your NGC models are correctly configured!")
        logger.info("   The issue was in the pipeline code, not the models themselves.")
    else:
        logger.info("\n⚠️ RESULT: Models need troubleshooting.")
        logger.info("   Check model file paths and dependencies.")
