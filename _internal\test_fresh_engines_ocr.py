#!/usr/bin/env python3
"""
Test the rebuilt TensorRT engines with actual subtitle images
"""

import sys
sys.path.append('.')

import asyncio
from pathlib import Path
import logging

# Set up logging to see the OCR process
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def test_ocr_with_fresh_engines():
    """Test OCR with the newly built TensorRT engines"""
    print("🧪 Testing OCR with fresh TensorRT engines...")
    
    try:
        from utils.ngc_ocr_pipeline import step3_perform_ngc_ocr
        
        # Look for test images
        workspace_path = Path("workspace")
        test_images = []
        
        # Check for existing processed images in various stages
        for stage_dir in ["2_downloaded_and_organized", "3_mkv_cleaned_subtitles_extracted"]:
            stage_path = workspace_path / stage_dir
            if stage_path.exists():
                for movie_dir in stage_path.iterdir():
                    if movie_dir.is_dir():
                        image_files = list(movie_dir.glob("*.png")) + list(movie_dir.glob("*.jpg"))
                        if image_files:
                            test_images.extend(image_files[:3])  # Take first 3 images
                            break
                if test_images:
                    break
        
        if not test_images:
            print("❌ No test images found in workspace directories")
            return
        
        print(f"📸 Found {len(test_images)} test images:")
        for i, img_path in enumerate(test_images):
            print(f"   {i+1}. {img_path}")
        
        # Load images
        from PIL import Image
        loaded_images = []
        for img_path in test_images:
            try:
                img = Image.open(img_path)
                loaded_images.append(img)
                print(f"✅ Loaded {img_path.name}: {img.size}")
            except Exception as e:
                print(f"❌ Failed to load {img_path.name}: {e}")
        
        if not loaded_images:
            print("❌ No images could be loaded")
            return
        
        # Run OCR
        print(f"\n🔍 Running OCR on {len(loaded_images)} images...")
        print("📊 Watch for 'Region X,Y-Z,W: text' outputs to see recognition results")
        print("")
        
        ocr_results = await step3_perform_ngc_ocr(loaded_images)
        
        print(f"\n✅ OCR completed - got {len(ocr_results)} results:")
        for i, result in enumerate(ocr_results):
            print(f"   Image {i+1}: '{result}'")
        
        # Check for successful multi-word recognition
        successful_results = [r for r in ocr_results if r and len(r.split()) > 1]
        single_char_results = [r for r in ocr_results if r and len(r) == 1]
        
        print(f"\n📊 Analysis:")
        print(f"   Multi-word results: {len(successful_results)}")
        print(f"   Single character results: {len(single_char_results)}")
        print(f"   Empty results: {len([r for r in ocr_results if not r])}")
        
        if successful_results:
            print(f"\n🎉 SUCCESS: Found multi-word recognition!")
            for result in successful_results:
                print(f"   ✅ '{result}'")
        else:
            print(f"\n⚠️  No multi-word results found - may need sliding window tuning")
        
    except Exception as e:
        print(f"❌ OCR test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ocr_with_fresh_engines())
