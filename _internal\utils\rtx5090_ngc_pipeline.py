"""
RTX 5090 Optimized NGC OCR Pipeline with TensorRT Acceleration
Integrates advanced TensorRT optimization for maximum performance
"""
import asyncio
import logging
import numpy as np
import cv2
from pathlib import Path
from typing import List, Tuple, Dict, Optional
import time

# TensorRT and CUDA imports
try:
    import tensorrt as trt
    import pycuda.driver as cuda
    import pycuda.autoinit
    TENSORRT_AVAILABLE = True
except ImportError:
    TENSORRT_AVAILABLE = False
    print("⚠️ TensorRT not available - falling back to ONNX")

# ONNX Runtime fallback
try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False

logger = logging.getLogger(__name__)

class RTX5090_NGC_OCR_Engine:
    """
    High-performance NGC OCR engine optimized for RTX 5090 with TensorRT
    """
    
    def __init__(self, workspace_path: str):
        self.workspace = Path(workspace_path)
        self.models_dir = self.workspace / "_internal" / "models" / "ngc_ocr"
        self.tensorrt_dir = self.models_dir / "tensorrt"
        
        # Performance mode selection
        self.performance_modes = {
            'maximum_throughput': {
                'detection_engine': 'ocdnet_rtx5090_fp16_batch32.trt',
                'recognition_engine': 'ocrnet_rtx5090_fp16_batch64.trt',
                'detection_batch': 32,
                'recognition_batch': 64
            },
            'balanced': {
                'detection_engine': 'ocdnet_rtx5090_fp16_batch8.trt', 
                'recognition_engine': 'ocrnet_rtx5090_fp16_batch16.trt',
                'detection_batch': 8,
                'recognition_batch': 16
            },
            'low_latency': {
                'detection_engine': 'ocdnet_rtx5090_fp16_batch1.trt',
                'recognition_engine': 'ocrnet_rtx5090_fp16_batch4.trt', 
                'detection_batch': 1,
                'recognition_batch': 4
            }
        }
        
        # Auto-select performance mode based on TensorRT availability
        self.use_tensorrt = TENSORRT_AVAILABLE
        self.current_mode = 'balanced'  # Default mode
        
        # Initialize engines
        self.detection_engine = None
        self.recognition_engine = None
        self.detection_context = None
        self.recognition_context = None
        
        # ONNX fallback sessions
        self.detection_session = None
        self.recognition_session = None
        
        self._initialize_engines()
        
    def _initialize_engines(self):
        """Initialize TensorRT engines or fallback to ONNX"""
        if self.use_tensorrt and self._check_tensorrt_engines():
            logger.info("🚀 Initializing RTX 5090 TensorRT engines...")
            self._load_tensorrt_engines()
        else:
            logger.info("📦 Falling back to ONNX Runtime with CUDA...")
            self._load_onnx_engines()
            
    def _check_tensorrt_engines(self) -> bool:
        """Check if optimized TensorRT engines exist"""
        mode_config = self.performance_modes[self.current_mode]
        
        detection_engine_path = self.tensorrt_dir / mode_config['detection_engine']
        recognition_engine_path = self.tensorrt_dir / mode_config['recognition_engine']
        
        engines_exist = detection_engine_path.exists() and recognition_engine_path.exists()
        
        if not engines_exist:
            logger.warning("⚠️ Optimized TensorRT engines not found")
            logger.info("💡 Run rtx5090_tensorrt_optimizer.py to create optimized engines")
            
        return engines_exist
        
    def _load_tensorrt_engines(self):
        """Load TensorRT engines for maximum performance"""
        try:
            mode_config = self.performance_modes[self.current_mode]
            
            # Load detection engine
            detection_engine_path = self.tensorrt_dir / mode_config['detection_engine']
            with open(detection_engine_path, 'rb') as f:
                detection_runtime = trt.Runtime(trt.Logger(trt.Logger.WARNING))
                self.detection_engine = detection_runtime.deserialize_cuda_engine(f.read())
                self.detection_context = self.detection_engine.create_execution_context()
                
            # Load recognition engine  
            recognition_engine_path = self.tensorrt_dir / mode_config['recognition_engine']
            with open(recognition_engine_path, 'rb') as f:
                recognition_runtime = trt.Runtime(trt.Logger(trt.Logger.WARNING))
                self.recognition_engine = recognition_runtime.deserialize_cuda_engine(f.read())
                self.recognition_context = self.recognition_engine.create_execution_context()
                
            logger.info(f"✅ TensorRT engines loaded in {self.current_mode} mode")
            logger.info(f"   Detection: {mode_config['detection_engine']}")
            logger.info(f"   Recognition: {mode_config['recognition_engine']}")
            
        except Exception as e:
            logger.error(f"❌ Failed to load TensorRT engines: {e}")
            logger.info("🔄 Falling back to ONNX Runtime...")
            self.use_tensorrt = False
            self._load_onnx_engines()
            
    def _load_onnx_engines(self):
        """Load ONNX engines as fallback"""
        try:
            if not ONNX_AVAILABLE:
                raise ImportError("ONNX Runtime not available")
                
            # ONNX model paths
            ocdnet_onnx = self.models_dir / "ocdnet_v2.4" / "ocdnet_vdeployable_onnx_v2.4" / "ocdnet_fan_tiny_2x_icdar_pruned.onnx"
            ocrnet_onnx = self.models_dir / "ocrnet_v2.1.1" / "ocrnet_vdeployable_v2.1.1" / "ocrnet-vit-pcb.onnx"
            
            # Create ONNX sessions with CUDA optimization
            providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
            
            session_options = ort.SessionOptions()
            session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
            
            self.detection_session = ort.InferenceSession(str(ocdnet_onnx), 
                                                        providers=providers,
                                                        sess_options=session_options)
            self.recognition_session = ort.InferenceSession(str(ocrnet_onnx),
                                                          providers=providers, 
                                                          sess_options=session_options)
            
            logger.info("✅ ONNX Runtime engines loaded with CUDA acceleration")
            
        except Exception as e:
            logger.error(f"❌ Failed to load ONNX engines: {e}")
            raise RuntimeError("No suitable OCR engine available")
            
    def set_performance_mode(self, mode: str):
        """
        Set performance mode for optimal RTX 5090 utilization
        
        Args:
            mode: 'maximum_throughput', 'balanced', or 'low_latency'
        """
        if mode not in self.performance_modes:
            logger.warning(f"Invalid mode: {mode}. Using 'balanced'")
            mode = 'balanced'
            
        if mode != self.current_mode:
            logger.info(f"🔄 Switching to {mode} performance mode...")
            self.current_mode = mode
            
            if self.use_tensorrt:
                self._load_tensorrt_engines()
                
    async def process_subtitle_images(self, images: List[np.ndarray]) -> List[str]:
        """
        Process subtitle images with RTX 5090 optimized pipeline
        
        Args:
            images: List of preprocessed subtitle images
            
        Returns:
            List of recognized text strings
        """
        if not images:
            return []
            
        logger.info(f"🎯 Processing {len(images)} images with RTX 5090 optimized pipeline")
        logger.info(f"   Mode: {self.current_mode}")
        logger.info(f"   Engine: {'TensorRT' if self.use_tensorrt else 'ONNX Runtime'}")
        
        start_time = time.time()
        
        if self.use_tensorrt:
            results = await self._process_with_tensorrt(images)
        else:
            results = await self._process_with_onnx(images)
            
        duration = time.time() - start_time
        fps = len(images) / duration if duration > 0 else 0
        
        logger.info(f"✅ Processing complete: {duration:.2f}s ({fps:.1f} FPS)")
        
        return results
        
    async def _process_with_tensorrt(self, images: List[np.ndarray]) -> List[str]:
        """Process images using TensorRT engines for maximum performance"""
        mode_config = self.performance_modes[self.current_mode]
        detection_batch = mode_config['detection_batch']
        recognition_batch = mode_config['recognition_batch']
        
        results = [''] * len(images)
        
        try:
            # Process detection in batches
            for i in range(0, len(images), detection_batch):
                batch_end = min(i + detection_batch, len(images))
                batch_images = images[i:batch_end]
                
                # Prepare batch tensor
                batch_tensor = self._prepare_detection_batch(batch_images)
                
                # Run TensorRT detection
                detection_outputs = self._run_tensorrt_detection(batch_tensor)
                
                # Parse detection results and extract text regions
                all_text_crops = []
                crop_to_image_mapping = []
                
                for batch_idx, img in enumerate(batch_images):
                    img_idx = i + batch_idx
                    
                    # Extract detection output for this image
                    img_detection = [output[batch_idx:batch_idx+1] for output in detection_outputs]
                    
                    # Parse text regions
                    text_regions = self._parse_detection_output(img_detection, img.shape[:2])
                    
                    # Extract text crops
                    for region in text_regions:
                        crop = self._extract_text_crop(img, region)
                        if crop is not None:
                            all_text_crops.append(crop)
                            crop_to_image_mapping.append(img_idx)
                
                # Process recognition in batches
                if all_text_crops:
                    crop_texts = await self._process_recognition_batch(all_text_crops, recognition_batch)
                    
                    # Map results back to images
                    crop_idx = 0
                    for batch_idx, img in enumerate(batch_images):
                        img_idx = i + batch_idx
                        img_detection = [output[batch_idx:batch_idx+1] for output in detection_outputs]
                        text_regions = self._parse_detection_output(img_detection, img.shape[:2])
                        
                        img_texts = []
                        for _ in text_regions:
                            if crop_idx < len(crop_texts):
                                img_texts.append(crop_texts[crop_idx])
                                crop_idx += 1
                        
                        results[img_idx] = ' '.join(img_texts) if img_texts else ''
                        
        except Exception as e:
            logger.error(f"❌ TensorRT processing failed: {e}")
            # Fallback to ONNX if TensorRT fails
            return await self._process_with_onnx(images)
            
        return results
        
    async def _process_with_onnx(self, images: List[np.ndarray]) -> List[str]:
        """Process images using ONNX Runtime as fallback"""
        # Use existing ONNX processing logic with optimized batching
        batch_size = 8  # Conservative batch size for ONNX
        results = [''] * len(images)
        
        try:
            for i in range(0, len(images), batch_size):
                batch_end = min(i + batch_size, len(images))
                batch_images = images[i:batch_end]
                
                # Process each image in the batch
                for batch_idx, img in enumerate(batch_images):
                    img_idx = i + batch_idx
                    
                    # Prepare single image tensor
                    img_tensor = self._prepare_single_image_tensor(img)
                    
                    # Run detection
                    det_input_name = self.detection_session.get_inputs()[0].name
                    detection_outputs = self.detection_session.run(None, {det_input_name: img_tensor})
                    
                    # Parse detection
                    text_regions = self._parse_detection_output(detection_outputs, img.shape[:2])
                    
                    if text_regions:
                        # Extract and recognize text
                        text_crops = [self._extract_text_crop(img, region) for region in text_regions]
                        text_crops = [crop for crop in text_crops if crop is not None]
                        
                        if text_crops:
                            crop_texts = await self._recognize_text_crops(text_crops)
                            results[img_idx] = ' '.join(crop_texts)
                            
        except Exception as e:
            logger.error(f"❌ ONNX processing failed: {e}")
            
        return results
        
    def _prepare_detection_batch(self, images: List[np.ndarray]) -> np.ndarray:
        """Prepare batch tensor for detection"""
        # Standard NGC OCDNet input: [batch, 3, 736, 1280]
        target_height, target_width = 736, 1280
        batch_size = len(images)
        
        batch_tensor = np.zeros((batch_size, 3, target_height, target_width), dtype=np.float32)
        
        for i, img in enumerate(images):
            # Resize and normalize
            resized = cv2.resize(img, (target_width, target_height))
            if len(resized.shape) == 3:
                # Convert BGR to RGB and normalize
                resized = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
                resized = resized.astype(np.float32) / 255.0
                # HWC to CHW
                batch_tensor[i] = resized.transpose(2, 0, 1)
            else:
                # Grayscale - expand to 3 channels
                resized = resized.astype(np.float32) / 255.0
                batch_tensor[i, 0] = batch_tensor[i, 1] = batch_tensor[i, 2] = resized
                
        return np.ascontiguousarray(batch_tensor)
        
    def _run_tensorrt_detection(self, batch_tensor: np.ndarray) -> List[np.ndarray]:
        """Run TensorRT detection inference"""
        # Allocate GPU memory
        input_size = batch_tensor.nbytes
        
        # Estimate output size (this depends on the specific model)
        batch_size = batch_tensor.shape[0]
        output_size = batch_size * 736 * 1280 * 4  # Estimated
        
        input_gpu = cuda.mem_alloc(input_size)
        output_gpu = cuda.mem_alloc(output_size)
        
        try:
            # Copy input to GPU
            cuda.memcpy_htod(input_gpu, batch_tensor)
            
            # Set dynamic batch size
            self.detection_context.set_input_shape(0, batch_tensor.shape)
            
            # Run inference
            self.detection_context.execute_v2([int(input_gpu), int(output_gpu)])
            
            # Copy output back
            output = np.empty((batch_size, 736, 1280), dtype=np.float32)
            cuda.memcpy_dtoh(output, output_gpu)
            
            return [output]
            
        finally:
            # Clean up GPU memory
            input_gpu.free()
            output_gpu.free()
            
    async def _process_recognition_batch(self, text_crops: List[np.ndarray], 
                                       batch_size: int) -> List[str]:
        """Process text recognition in optimized batches"""
        results = []
        
        for i in range(0, len(text_crops), batch_size):
            batch_crops = text_crops[i:i + batch_size]
            
            if self.use_tensorrt:
                batch_texts = self._run_tensorrt_recognition(batch_crops)
            else:
                batch_texts = await self._recognize_text_crops(batch_crops)
                
            results.extend(batch_texts)
            
        return results
        
    def _run_tensorrt_recognition(self, text_crops: List[np.ndarray]) -> List[str]:
        """Run TensorRT text recognition"""
        # Prepare batch tensor for recognition
        batch_tensor = self._prepare_recognition_batch(text_crops)
        
        # Run TensorRT inference (implementation similar to detection)
        # This would need the specific OCRNet output format and decoding logic
        
        # For now, return placeholder results
        return [''] * len(text_crops)
        
    # Additional helper methods would go here...
    def _prepare_single_image_tensor(self, img: np.ndarray) -> np.ndarray:
        """Prepare single image tensor for ONNX processing"""
        target_height, target_width = 736, 1280
        
        resized = cv2.resize(img, (target_width, target_height))
        if len(resized.shape) == 3:
            resized = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
            resized = resized.astype(np.float32) / 255.0
            tensor = resized.transpose(2, 0, 1)
        else:
            resized = resized.astype(np.float32) / 255.0
            tensor = np.stack([resized, resized, resized])
            
        return np.expand_dims(tensor, axis=0)
        
    def _parse_detection_output(self, detection_outputs: List[np.ndarray], 
                              original_size: Tuple[int, int]) -> List[Dict]:
        """Parse detection output to extract text regions"""
        # This would contain the specific logic for parsing OCDNet outputs
        # and converting them to bounding boxes/polygons
        # For now, return empty list
        return []
        
    def _extract_text_crop(self, img: np.ndarray, region: Dict) -> Optional[np.ndarray]:
        """Extract text crop from image based on detected region"""
        # Implementation would extract the text region from the image
        # For now, return None
        return None
        
    async def _recognize_text_crops(self, text_crops: List[np.ndarray]) -> List[str]:
        """Recognize text from crops using ONNX"""
        # Implementation would process text crops through OCRNet
        # For now, return empty strings
        return [''] * len(text_crops)

# Update the main NGC OCR pipeline configuration
NGC_OCR_CONFIG_RTX5090 = {
    # Use RTX 5090 optimized engine
    "engine_class": RTX5090_NGC_OCR_Engine,
    
    # Performance tuning for RTX 5090
    "performance_mode": "balanced",  # Options: maximum_throughput, balanced, low_latency
    "enable_adaptive_batching": True,
    "gpu_memory_limit": 20,  # Conservative limit (32GB available)
    
    # TensorRT optimizations
    "tensorrt_precision": "fp16",
    "tensorrt_optimization_level": 5,
    "tensorrt_workspace_gb": 4,
    
    # Batch processing
    "max_detection_batch": 32,
    "max_recognition_batch": 64,
    "enable_cuda_graphs": True,
    
    # Memory management
    "clear_cache_between_batches": True,
    "pin_memory": True,
    "non_blocking_transfer": True,
}

async def process_subtitles_with_rtx5090(images: List[np.ndarray], 
                                       workspace_path: str,
                                       performance_mode: str = "balanced") -> List[str]:
    """
    Main entry point for RTX 5090 optimized subtitle processing
    
    Args:
        images: Preprocessed subtitle images
        workspace_path: Path to PlexMovieAutomator workspace
        performance_mode: Performance optimization mode
        
    Returns:
        List of recognized text strings
    """
    
    logger.info("🚀 Starting RTX 5090 optimized NGC OCR processing...")
    
    # Initialize optimized engine
    engine = RTX5090_NGC_OCR_Engine(workspace_path)
    engine.set_performance_mode(performance_mode)
    
    # Process images
    results = await engine.process_subtitle_images(images)
    
    logger.info(f"✅ RTX 5090 processing complete: {len(results)} results")
    
    return results
