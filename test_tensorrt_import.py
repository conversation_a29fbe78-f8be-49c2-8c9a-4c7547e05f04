#!/usr/bin/env python3
"""
Test TensorRT import with PATH fix
"""

import os
import sys
from pathlib import Path

def test_tensorrt_import():
    """Test if TensorRT can be imported with PATH fix"""
    print("🔍 Testing TensorRT import...")
    
    # Add TensorRT lib directory to PATH
    tensorrt_lib_path = Path("_internal/tools/TensorRT/lib")
    if tensorrt_lib_path.exists():
        print(f"📁 Found TensorRT lib directory: {tensorrt_lib_path.absolute()}")
        current_path = os.environ.get("PATH", "")
        os.environ["PATH"] = str(tensorrt_lib_path.absolute()) + os.pathsep + current_path
        print("✅ Added TensorRT lib to PATH")
    else:
        print(f"❌ TensorRT lib directory not found: {tensorrt_lib_path.absolute()}")
        return False
    
    # Try to import TensorRT
    try:
        print("🔧 Attempting TensorRT import...")
        import tensorrt as trt
        print(f"✅ TensorRT imported successfully! Version: {trt.__version__}")
        return True
    except Exception as e:
        print(f"❌ TensorRT import failed: {e}")
        return False

if __name__ == "__main__":
    success = test_tensorrt_import()
    if success:
        print("\n🎉 TensorRT is working!")
    else:
        print("\n💥 TensorRT import failed!")
    sys.exit(0 if success else 1)
