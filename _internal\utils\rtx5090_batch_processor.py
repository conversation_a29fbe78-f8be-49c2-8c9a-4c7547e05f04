
"""
RTX 5090 Optimized Batch Processing Pipeline for NGC OCR
Maximizes GPU utilization with intelligent batching
"""
import numpy as np
import tensorrt as trt
import pycuda.driver as cuda
import pycuda.autoinit
from typing import List, Tuple
import logging

class RTX5090_BatchProcessor:
    """
    Intelligent batch processor for RTX 5090 with adaptive batch sizing
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # RTX 5090 optimized batch sizes based on memory and compute
        self.detection_batch_sizes = {
            'high_throughput': 32,   # For processing many images
            'balanced': 8,           # For mixed workloads  
            'low_latency': 1         # For real-time processing
        }
        
        self.recognition_batch_sizes = {
            'high_throughput': 64,   # Text crops are smaller
            'balanced': 16,          # Balanced throughput/latency
            'low_latency': 4         # Low latency recognition
        }
        
        # Load appropriate engines based on workload
        self.current_mode = 'balanced'
        self._load_engines()
        
    def _load_engines(self):
        """Load TensorRT engines optimized for current mode"""
        models_dir = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\tensorrt")
        
        # Select engines based on mode
        if self.current_mode == 'high_throughput':
            detection_engine = models_dir / "ocdnet_rtx5090_fp16_batch32.trt"
            recognition_engine = models_dir / "ocrnet_rtx5090_fp16_batch64.trt"
        elif self.current_mode == 'balanced':
            detection_engine = models_dir / "ocdnet_rtx5090_fp16_batch8.trt"  
            recognition_engine = models_dir / "ocrnet_rtx5090_fp16_batch16.trt"
        else:  # low_latency
            detection_engine = models_dir / "ocdnet_rtx5090_fp16_batch1.trt"
            recognition_engine = models_dir / "ocrnet_rtx5090_fp16_batch4.trt"
            
        # Load engines with RTX 5090 optimizations
        self._load_detection_engine(detection_engine)
        self._load_recognition_engine(recognition_engine)
        
    def process_subtitle_batch(self, images: List[np.ndarray], 
                             adaptive_batching: bool = True) -> List[str]:
        """
        Process subtitle images with RTX 5090 optimized batching
        
        Args:
            images: List of subtitle images
            adaptive_batching: Automatically adjust batch size based on GPU load
            
        Returns:
            List of recognized text strings
        """
        
        if adaptive_batching:
            # Dynamically adjust batch size based on GPU memory and workload
            batch_size = self._calculate_optimal_batch_size(len(images))
        else:
            batch_size = self.detection_batch_sizes[self.current_mode]
            
        results = []
        
        # Process in optimized batches
        for i in range(0, len(images), batch_size):
            batch = images[i:i + batch_size]
            
            # Step 1: Text Detection (OCDNet)
            text_regions = self._detect_text_batch(batch)
            
            # Step 2: Extract and batch text crops
            all_text_crops = []
            crop_to_image_mapping = []
            
            for img_idx, regions in enumerate(text_regions):
                for region in regions:
                    crop = self._extract_text_crop(batch[img_idx], region)
                    all_text_crops.append(crop)
                    crop_to_image_mapping.append(i + img_idx)
            
            # Step 3: Text Recognition (OCRNet) with larger batches
            if all_text_crops:
                recognition_batch_size = self.recognition_batch_sizes[self.current_mode]
                crop_texts = self._recognize_text_batch(all_text_crops, recognition_batch_size)
                
                # Step 4: Assemble results back to original images
                batch_results = [''] * len(batch)
                crop_idx = 0
                
                for img_idx, regions in enumerate(text_regions):
                    texts_for_image = []
                    for _ in regions:
                        if crop_idx < len(crop_texts):
                            texts_for_image.append(crop_texts[crop_idx])
                            crop_idx += 1
                    
                    # Combine texts for this image
                    batch_results[img_idx] = ' '.join(texts_for_image)
                
                results.extend(batch_results)
            else:
                results.extend([''] * len(batch))
                
        return results
        
    def _calculate_optimal_batch_size(self, num_images: int) -> int:
        """
        Calculate optimal batch size based on RTX 5090 capabilities and workload
        """
        # RTX 5090 has 32GB VRAM - we can be aggressive with batch sizes
        if num_images >= 100:
            return 32  # High throughput for large workloads
        elif num_images >= 20:
            return 8   # Balanced for medium workloads
        else:
            return 4   # Smaller batches for few images
